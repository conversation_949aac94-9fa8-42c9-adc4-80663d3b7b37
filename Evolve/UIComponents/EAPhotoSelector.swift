import SwiftUI
import PhotosUI

/// 图片选择器组件
/// 支持多图片选择、预览、删除、拖拽排序等功能
/// 遵循社交APP图片上传通用规范
@MainActor
struct EAPhotoSelector: View {
    
    // MARK: - 绑定属性
    
    /// 选中的图片数据数组
    @Binding var selectedImages: [Data]
    
    /// 最大选择数量
    let maxSelectionCount: Int
    
    /// 错误处理回调
    let onError: (String) -> Void
    
    // MARK: - 状态属性
    
    /// PhotosPicker选择项
    @State private var selectedItems: [PhotosPickerItem] = []
    
    /// 加载状态
    @State private var isLoading = false
    
    /// 拖拽状态
    @State private var draggedItem: Data?
    
    // MARK: - 初始化
    
    init(
        selectedImages: Binding<[Data]>,
        maxSelectionCount: Int = EAImageProcessor.ImageLimits.maxImageCount,
        onError: @escaping (String) -> Void = { _ in }
    ) {
        self._selectedImages = selectedImages
        self.maxSelectionCount = maxSelectionCount
        self.onError = onError
    }
    
    // MARK: - 主视图
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题和计数
            headerView
            
            // 图片网格
            imageGridView
            
            // 添加按钮
            if selectedImages.count < maxSelectionCount {
                addPhotoButton
            }
        }
        .photosPicker(
            isPresented: .constant(false),
            selection: $selectedItems,
            maxSelectionCount: maxSelectionCount - selectedImages.count,
            matching: .images,
            photoLibrary: .shared()
        )
        .onChange(of: selectedItems) { _, newItems in
            Task { @MainActor in
                await loadSelectedPhotos(newItems)
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 标题视图
    private var headerView: some View {
        HStack {
            Text("添加图片")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            Text("\(selectedImages.count)/\(maxSelectionCount)")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
        }
    }
    
    /// 图片网格视图
    private var imageGridView: some View {
        let columns = Array(repeating: GridItem(.flexible(), spacing: 8), count: 3)
        
        return LazyVGrid(columns: columns, spacing: 8) {
            ForEach(Array(selectedImages.enumerated()), id: \.offset) { index, imageData in
                imagePreviewCell(imageData, at: index)
            }
        }
    }
    
    /// 图片预览单元格
    private func imagePreviewCell(_ imageData: Data, at index: Int) -> some View {
        ZStack(alignment: .topTrailing) {
            // 图片内容
            Group {
                if let uiImage = UIImage(data: imageData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: imageSize, height: imageSize)
                        .clipped()
                } else {
                    Rectangle()
                        .fill(.gray.opacity(0.3))
                        .frame(width: imageSize, height: imageSize)
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        )
                }
            }
            .cornerRadius(8)
            
            // 删除按钮
            Button(action: {
                removeImage(at: index)
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.red)
                    .background(Color.white, in: Circle())
            }
            .offset(x: 8, y: -8)
        }
        .onDrag {
            draggedItem = imageData
            return NSItemProvider(object: String(index) as NSString)
        }
        .onDrop(of: [.text], delegate: ImageDropDelegate(
            item: imageData,
            draggedItem: $draggedItem,
            selectedImages: $selectedImages
        ))
    }
    
    /// 添加图片按钮
    private var addPhotoButton: some View {
        let currentIsLoading = isLoading
        
        return PhotosPicker(
            selection: $selectedItems,
            maxSelectionCount: maxSelectionCount - selectedImages.count,
            matching: .images,
            photoLibrary: .shared()
        ) {
            HStack(spacing: 12) {
                Image(systemName: "plus.circle")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(Color.accentColor)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("添加图片")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Text("最多可选择 \(maxSelectionCount) 张图片")
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                Group {
                    if currentIsLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(Color.accentColor)
                    } else {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.5))
                    }
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
        }
        .disabled(currentIsLoading)
    }
    
    // MARK: - 计算属性
    
    /// 图片大小
    private var imageSize: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32 // 左右边距
        let spacing: CGFloat = 16 // 网格间距
        return (screenWidth - padding - spacing) / 3
    }
    
    // MARK: - 方法
    
    /// 加载选中的图片
    private func loadSelectedPhotos(_ items: [PhotosPickerItem]) async {
        isLoading = true
        defer { isLoading = false }
        
        var newImages: [Data] = []
        
        for item in items {
            do {
                if let data = try await item.loadTransferable(type: Data.self) {
                    // 验证图片
                    try validateImage(data)
                    newImages.append(data)
                }
            } catch let error as EAImageProcessor.ImageProcessingError {
                onError(error.localizedDescription)
            } catch {
                onError("图片加载失败：\(error.localizedDescription)")
            }
        }
        
        // 更新选中图片
        selectedImages.append(contentsOf: newImages)
        
        // 清空选择项
        selectedItems.removeAll()
    }
    
    /// 验证图片
    private func validateImage(_ data: Data) throws {
        // 检查文件大小
        guard data.count <= EAImageProcessor.ImageLimits.maxFileSize else {
            throw EAImageProcessor.ImageProcessingError.fileTooLarge
        }
        
        // 检查是否是有效图片
        guard UIImage(data: data) != nil else {
            throw EAImageProcessor.ImageProcessingError.invalidImageData
        }
    }
    
    /// 移除图片
    private func removeImage(at index: Int) {
        _ = withAnimation(.easeInOut(duration: 0.2)) {
            selectedImages.remove(at: index)
        }
    }
}

// MARK: - 拖拽代理

/// 图片拖拽代理
struct ImageDropDelegate: DropDelegate {
    let item: Data
    @Binding var draggedItem: Data?
    @Binding var selectedImages: [Data]
    
    func performDrop(info: DropInfo) -> Bool {
        draggedItem = nil
        return true
    }
    
    func dropEntered(info: DropInfo) {
        guard let draggedItem = draggedItem,
              draggedItem != item,
              let fromIndex = selectedImages.firstIndex(of: draggedItem),
              let toIndex = selectedImages.firstIndex(of: item) else {
            return
        }
        
        withAnimation(.spring()) {
            selectedImages.move(
                fromOffsets: IndexSet([fromIndex]),
                toOffset: toIndex > fromIndex ? toIndex + 1 : toIndex
            )
        }
    }
}

// MARK: - 预览

#Preview("图片选择器") {
    @Previewable @State var selectedImages: [Data] = []
    
    ZStack {
        Color.hexColor("002b20")
            .ignoresSafeArea()
        
        VStack {
            EAPhotoSelector(
                selectedImages: $selectedImages,
                onError: { error in
                    // 预览环境下的错误处理（生产环境会有实际处理）
                }
            )
            .padding()
            
            Spacer()
        }
    }
    .preferredColorScheme(.dark)
} 